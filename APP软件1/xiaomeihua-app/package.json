{"name": "xiaomeihua-ai", "productName": "小梅花AI智能客服", "version": "1.0.13", "description": "小梅花AI智能客服 - 支持URL匹配规则", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "cross-env NODE_ENV=development electron .", "build:mac": "node scripts/validate-config.js && electron-builder --mac", "build:win": "node scripts/validate-config.js && electron-builder --win", "build:x64": "node scripts/validate-config.js && electron-builder --mac --x64", "build:arm64": "node scripts/validate-config.js && electron-builder --mac --arm64", "build:all": "node scripts/validate-config.js && electron-builder --mac --x64 --arm64", "build:verified": "node scripts/validate-config.js && npm run clean && npm run build:all && npm run verify:signatures", "verify:signatures": "node scripts/verify-signatures.js", "clean": "<PERSON><PERSON><PERSON> dist", "postpackage": "node scripts/post-package-fix.js", "fix:ultimate": "node scripts/ultimate-fix.js", "build:fixed": "node scripts/validate-config.js && npm run clean && npm run build:all && npm run fix:ultimate", "build:final": "node scripts/validate-config.js && node scripts/final-dmg-builder.js", "build:dmg": "npm run clean && npm run build:mac && npm run package:dmg", "package:dmg": "node scripts/create-dmg.js"}, "author": "小梅花AI科技", "license": "UNLICENSED", "private": true, "build": {"appId": "cn.xiaomeihuakefu.app", "productName": "小梅花AI智能客服", "copyright": "Copyright © 2025 小梅花AI科技", "directories": {"output": "dist"}, "mac": {"category": "public.app-category.business", "target": [{"target": "dmg", "arch": "arm64"}, {"target": "dmg", "arch": "x64"}], "icon": "build/icon.icns", "hardenedRuntime": false, "gatekeeperAssess": false, "extendInfo": {"CFBundleVersion": "1.0.12", "CFBundleShortVersionString": "1.0.12", "NSHighResolutionCapable": true, "LSMinimumSystemVersion": "10.15.0"}}, "dmg": {"title": "${productName}-${version}", "icon": "build/icon.icns", "iconSize": 100, "contents": [{"x": 300, "y": 100, "type": "file", "path": "Mac安装教程.png"}, {"x": 160, "y": 280}, {"x": 480, "y": 280, "type": "link", "path": "/Applications"}], "window": {"width": 600, "height": 480, "x": 438, "y": 230}, "backgroundColor": "#ffffff", "artifactName": "${productName}-${version}-${arch}.${ext}", "sign": false}, "afterPack": "scripts/after-pack-sign.js", "afterAllArtifactBuild": "scripts/after-dmg-fix.js", "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "build/icon.ico", "artifactName": "${productName} Setup ${version}.${ext}", "publisherName": "小梅花AI科技"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "installerLanguages": ["zh_CN"], "language": "2052", "artifactName": "${productName} Setup ${version}.${ext}", "deleteAppDataOnUninstall": false, "displayLanguageSelector": false, "installerIcon": "build/icon.ico", "uninstallerIcon": "build/icon.ico", "installerHeaderIcon": "build/icon.ico"}, "files": ["src/**/*", "package.json", "node_modules/**/*", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*/{.editorconfig,.gitignore,.travis.yml}", "!**/node_modules/*/{*.d.ts,*.map}", "!**/node_modules/*/docs/**", "!**/node_modules/*/doc/**", "!**/node_modules/*/man/**", "!**/node_modules/*/coverage/**", "!**/node_modules/*/.nyc_output/**", "!**/node_modules/*/bench/**", "!**/node_modules/*/benchmark/**", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,__pycache__,thumbs.db,.gitkeep}", "!build/**", "!scripts/**", "!dist/**", "!release/**", "!test*.js", "!verify*.js", "!debug*.js", "!run*.js", "!simple*.js", "!*.md", "!.giti<PERSON>re", "!.eslintrc*", "!.prettierrc*", "!tsconfig.json", "!false/**", "!*.bat", "!*.crx"], "extraResources": [{"from": "resources/icon.ico", "to": "resources/icon.ico"}], "publish": null, "asarUnpack": ["src/preload-browser.js", "src/preload.js", "src/popup-preload.js", "src/agreement-preload.js", "src/about-preload.js", "src/renderer/popup.html", "src/renderer/agreement.html", "src/renderer/about.html"]}, "devDependencies": {"cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.9.1", "rimraf": "^5.0.5"}, "dependencies": {"axios": "^1.6.2", "electron-store": "^8.1.0", "electron-updater": "^6.6.2", "uuid": "^9.0.1"}, "configLockInfo": {"timestamp": "2025-08-05T21:44:00.000Z", "version": "1.0.0-LOCKED", "status": "PERMANENTLY_LOCKED", "description": "DMG配置已永久锁定：窗口600x480，位置{438,230}，图标100px，包含签名+教程图片+完美布局。除非明确要求，否则禁止修改！", "lockedComponents": ["after-dmg-fix.js", "package.json[build.dmg]", "AppleScript窗口设置", "双阶段DMG创建流程", "签名验证流程"]}}